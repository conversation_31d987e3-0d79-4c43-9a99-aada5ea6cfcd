import { api, handleApiResponse } from './base.api'
import type {
  Budget,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  BudgetFilters,
  BudgetStats,
  BudgetReport,
  BudgetProgress,
  BudgetResponse,
  BudgetsResponse,
  BudgetStatsResponse,
  BudgetReportResponse,
  BudgetProgressResponse,
  PaginatedBudgetsResponse,
} from '@/types/budget.types'

// Backend response type (different from frontend Budget type)
interface BackendBudgetResponse {
  id: string
  plannedAmount: number
  month: number
  year: number
  categoryId: string
  familyMemberId?: string
  createdAt: string
  updatedAt: string
  version: number
  category: {
    id: string
    name: string
    color?: string
    parent?: {
      id: string
      name: string
      color?: string
    }
  }
  familyMember?: {
    id: string
    name: string
    color: string
  }
  progress?: {
    actualAmount: number
    remainingAmount: number
    percentageUsed: number
    percentageRemaining: number
    isOverBudget: boolean
    overBudgetAmount?: number
    transactionCount: number
    averageTransactionAmount: number
    daysInPeriod: number
    daysRemaining: number
    dailyBudgetRemaining: number
    projectedTotal?: number
    status: 'under_budget' | 'on_track' | 'over_budget' | 'warning'
  }
}

interface BackendPaginatedResponse {
  data: BackendBudgetResponse[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Transform backend response to frontend Budget format
function transformBudgetResponse(backendBudget: BackendBudgetResponse): Budget {
  console.log('🔍 DEBUG: transformBudgetResponse input', {
    backendBudget,
    hasCategory: !!backendBudget.category,
    categoryId: backendBudget.categoryId,
    timestamp: new Date().toISOString()
  })

  const progress = backendBudget.progress

  // Safety check for category
  if (!backendBudget.category) {
    console.error('🚨 ERROR: backendBudget.category is undefined!', {
      backendBudget,
      categoryId: backendBudget.categoryId
    })
    throw new Error(`Category data missing for budget ${backendBudget.id}. CategoryId: ${backendBudget.categoryId}`)
  }

  return {
    id: backendBudget.id,
    plannedAmount: backendBudget.plannedAmount,
    spentAmount: progress?.actualAmount || 0,
    remainingAmount: progress?.remainingAmount || backendBudget.plannedAmount,
    month: backendBudget.month,
    year: backendBudget.year,
    categoryId: backendBudget.categoryId,
    familyMemberId: backendBudget.familyMemberId || null,
    progress: progress?.percentageUsed || 0,
    status: progress?.status === 'warning' ? 'on_track' : (progress?.status || 'under_budget'),
    createdAt: backendBudget.createdAt,
    updatedAt: backendBudget.updatedAt,
    category: {
      id: backendBudget.category.id,
      name: backendBudget.category.name,
      color: backendBudget.category.color,
      ...(backendBudget.category.parent && {
        parent: {
          id: backendBudget.category.parent.id,
          name: backendBudget.category.parent.name,
          color: backendBudget.category.parent.color
        }
      })
    },
    ...(backendBudget.familyMember && {
      familyMember: {
        id: backendBudget.familyMember.id,
        name: backendBudget.familyMember.name,
        avatar: undefined // Backend doesn't have avatar field
      }
    })
  }
}

export const budgetsApi = {
  /**
   * Get all budgets with filters and pagination
   */
  async getAll(filters: BudgetFilters = {}): Promise<PaginatedBudgetsResponse> {
    const params = new URLSearchParams()

    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })

    const backendResponse: BackendPaginatedResponse = await handleApiResponse(
      api.get(`/budgets?${params.toString()}`)
    )

    // Transform backend response to frontend format
    return {
      data: backendResponse.data.map(transformBudgetResponse),
      pagination: backendResponse.pagination
    }
  },

  /**
   * Get budget by ID
   */
  async getById(id: string, includeProgress = false): Promise<Budget> {
    const params = includeProgress ? '?includeProgress=true' : ''
    const backendResponse: BackendBudgetResponse = await handleApiResponse(
      api.get(`/budgets/${id}${params}`)
    )
    return transformBudgetResponse(backendResponse)
  },

  /**
   * Create a new budget
   */
  async create(data: CreateBudgetRequest): Promise<Budget> {
    const backendResponse: BackendBudgetResponse = await handleApiResponse(
      api.post('/budgets', data)
    )
    return transformBudgetResponse(backendResponse)
  },

  /**
   * Update budget
   */
  async update(id: string, data: UpdateBudgetRequest): Promise<Budget> {
    const backendResponse: BackendBudgetResponse = await handleApiResponse(
      api.put(`/budgets/${id}`, data)
    )
    return transformBudgetResponse(backendResponse)
  },

  /**
   * Delete budget (soft delete)
   */
  async delete(id: string): Promise<void> {
    return handleApiResponse(api.delete(`/budgets/${id}`))
  },

  /**
   * Get budget progress details
   */
  async getProgress(id: string): Promise<BudgetProgress> {
    return handleApiResponse(api.get(`/budgets/${id}/progress`))
  },

  /**
   * Get budgets statistics
   */
  async getStats(filters?: Partial<BudgetFilters>): Promise<BudgetStats> {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }

    const queryString = params.toString()
    const url = queryString ? `/budgets/stats?${queryString}` : '/budgets/stats'
    
    return handleApiResponse(api.get(url))
  },

  /**
   * Get budget report
   */
  async getReport(filters?: Partial<BudgetFilters>): Promise<BudgetReport> {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }

    const queryString = params.toString()
    const url = queryString ? `/budgets/report?${queryString}` : '/budgets/report'
    
    return handleApiResponse(api.get(url))
  },

  /**
   * Get budgets by category
   */
  async getByCategory(categoryId: string, filters: Omit<BudgetFilters, 'categoryId'> = {}): Promise<PaginatedBudgetsResponse> {
    return this.getAll({ ...filters, categoryId })
  },

  /**
   * Get budgets by family member
   */
  async getByFamilyMember(familyMemberId: string, filters: Omit<BudgetFilters, 'familyMemberId'> = {}): Promise<PaginatedBudgetsResponse> {
    return this.getAll({ ...filters, familyMemberId })
  },

  /**
   * Get budgets by month/year
   */
  async getByPeriod(month: number, year: number, filters: Omit<BudgetFilters, 'month' | 'year'> = {}): Promise<PaginatedBudgetsResponse> {
    return this.getAll({ ...filters, month, year })
  },

  /**
   * Get current month budgets
   */
  async getCurrentMonth(filters: Omit<BudgetFilters, 'month' | 'year'> = {}): Promise<PaginatedBudgetsResponse> {
    const now = new Date()
    const currentMonth = now.getMonth() + 1
    const currentYear = now.getFullYear()
    
    return this.getByPeriod(currentMonth, currentYear, filters)
  },

  /**
   * Get recent budgets
   */
  async getRecent(limit = 5): Promise<Budget[]> {
    const response = await this.getAll({
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
    return response.data
  },

  /**
   * Search budgets by category name
   */
  async search(query: string, filters: BudgetFilters = {}): Promise<PaginatedBudgetsResponse> {
    // Note: This would need backend support for search
    // For now, we'll filter on the frontend after getting results
    const response = await this.getAll(filters)
    
    const filteredData = response.data.filter(budget =>
      budget.category.name.toLowerCase().includes(query.toLowerCase()) ||
      (budget.familyMember?.name.toLowerCase().includes(query.toLowerCase()))
    )

    return {
      ...response,
      data: filteredData,
      pagination: {
        ...response.pagination,
        total: filteredData.length,
        totalPages: Math.ceil(filteredData.length / (filters.limit || 20))
      }
    }
  },

  /**
   * Get over-budget items
   */
  async getOverBudget(filters: BudgetFilters = {}): Promise<Budget[]> {
    const response = await this.getAll({ ...filters, includeProgress: true })
    return response.data.filter(budget => budget.status === 'over_budget')
  },

  /**
   * Get budgets needing attention (over budget or near limit)
   */
  async getNeedingAttention(filters: BudgetFilters = {}): Promise<Budget[]> {
    const response = await this.getAll({ ...filters, includeProgress: true })
    return response.data.filter(budget => 
      budget.status === 'over_budget' || 
      (budget.status === 'on_track' && budget.progress > 80)
    )
  }
}

export default budgetsApi
