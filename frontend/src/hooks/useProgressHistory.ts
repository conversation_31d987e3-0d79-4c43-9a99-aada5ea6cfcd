import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { progressHistoryApi } from '../lib/api/progress-history.api'
import type { ProgressHistoryFilters, UpdateProgressHistoryData } from '../types/progress-history.types'
import { toast } from 'react-hot-toast'

// Query keys
export const progressHistoryKeys = {
  all: ['progress-history'] as const,
  lists: () => [...progressHistoryKeys.all, 'list'] as const,
  list: (filters: ProgressHistoryFilters) => [...progressHistoryKeys.lists(), filters] as const,
  byGoal: (goalId: string) => [...progressHistoryKeys.all, 'goal', goalId] as const,
  recent: () => [...progressHistoryKeys.all, 'recent'] as const,
}

/**
 * Hook to get all progress history with filters
 */
export function useProgressHistory(filters: ProgressHistoryFilters = {}) {
  return useQuery({
    queryKey: progressHistoryKeys.list(filters),
    queryFn: () => progressHistoryApi.getAll(filters),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

/**
 * Hook to get progress history for a specific goal
 */
export function useProgressHistoryByGoal(goalId: string, filters: Omit<ProgressHistoryFilters, 'goalId'> = {}) {
  return useQuery({
    queryKey: [...progressHistoryKeys.byGoal(goalId), filters],
    queryFn: () => progressHistoryApi.getByGoal(goalId, filters),
    enabled: !!goalId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

/**
 * Hook to get recent progress updates
 */
export function useRecentProgressHistory(limit = 10) {
  return useQuery({
    queryKey: [...progressHistoryKeys.recent(), limit],
    queryFn: () => progressHistoryApi.getRecent(limit),
    staleTime: 1000 * 60 * 2, // 2 minutes
  })
}

/**
 * Hook to update progress history entry description
 */
export function useUpdateProgressHistoryDescription() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProgressHistoryData }) =>
      progressHistoryApi.updateDescription(id, data),
    onSuccess: (response) => {
      // Invalidate and refetch progress history queries
      queryClient.invalidateQueries({ queryKey: progressHistoryKeys.all })
      
      toast.success('Descrição atualizada com sucesso!')
    },
    onError: (error: any) => {
      console.error('Erro ao atualizar descrição:', error)
      toast.error('Erro ao atualizar descrição')
    },
  })
}

/**
 * Hook to delete progress history entry
 */
export function useDeleteProgressHistory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => progressHistoryApi.delete(id),
    onSuccess: () => {
      // Invalidate and refetch progress history queries
      queryClient.invalidateQueries({ queryKey: progressHistoryKeys.all })
      
      toast.success('Entrada do histórico deletada com sucesso!')
    },
    onError: (error: any) => {
      console.error('Erro ao deletar entrada do histórico:', error)
      toast.error('Erro ao deletar entrada do histórico')
    },
  })
}

/**
 * Hook to search progress history
 */
export function useSearchProgressHistory(query: string, filters: ProgressHistoryFilters = {}) {
  return useQuery({
    queryKey: [...progressHistoryKeys.list(filters), 'search', query],
    queryFn: () => progressHistoryApi.search(query, filters),
    enabled: query.length >= 2, // Only search when query has at least 2 characters
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
