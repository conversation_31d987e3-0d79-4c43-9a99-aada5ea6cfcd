import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { budgetsApi } from '@/lib/api'
import type {
  Budget,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  BudgetFilters,
  BudgetStats,
  BudgetReport,
  BudgetProgress,
} from '@/types/budget.types'

// Query keys
export const budgetKeys = {
  all: ['budgets'] as const,
  lists: () => [...budgetKeys.all, 'list'] as const,
  list: (filters: BudgetFilters) => [...budgetKeys.lists(), filters] as const,
  details: () => [...budgetKeys.all, 'detail'] as const,
  detail: (id: string) => [...budgetKeys.details(), id] as const,
  stats: () => [...budgetKeys.all, 'stats'] as const,
  statsWithFilters: (filters?: Partial<BudgetFilters>) => [...budgetKeys.stats(), filters] as const,
  report: () => [...budgetKeys.all, 'report'] as const,
  reportWithFilters: (filters?: Partial<BudgetFilters>) => [...budgetKeys.report(), filters] as const,
  progress: (id: string) => [...budgetKeys.all, 'progress', id] as const,
  byCategory: (categoryId: string) => [...budgetKeys.all, 'category', categoryId] as const,
  byFamilyMember: (familyMemberId: string) => [...budgetKeys.all, 'familyMember', familyMemberId] as const,
  byPeriod: (month: number, year: number) => [...budgetKeys.all, 'period', month, year] as const,
  currentMonth: () => [...budgetKeys.all, 'currentMonth'] as const,
}

/**
 * Hook to fetch all budgets with filters
 */
export function useBudgets(filters: BudgetFilters = {}) {
  return useQuery({
    queryKey: budgetKeys.list(filters),
    queryFn: () => budgetsApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch a single budget by ID
 */
export function useBudget(id: string, includeProgress = false) {
  return useQuery({
    queryKey: budgetKeys.detail(id),
    queryFn: () => budgetsApi.getById(id, includeProgress),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch budget statistics
 * TODO: Implementar endpoint /budgets/stats no backend
 */
export function useBudgetStats(filters?: Partial<BudgetFilters>) {
  return useQuery({
    queryKey: budgetKeys.statsWithFilters(filters),
    queryFn: () => Promise.resolve(null), // Temporariamente retorna null
    enabled: false, // Desabilita a query até o endpoint estar disponível
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch budget report
 */
export function useBudgetReport(filters?: Partial<BudgetFilters>) {
  return useQuery({
    queryKey: budgetKeys.reportWithFilters(filters),
    queryFn: () => budgetsApi.getReport(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch budget progress
 */
export function useBudgetProgress(id: string) {
  return useQuery({
    queryKey: budgetKeys.progress(id),
    queryFn: () => budgetsApi.getProgress(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute (progress changes frequently)
  })
}

/**
 * Hook to fetch budgets by category
 */
export function useBudgetsByCategory(categoryId: string, filters: Omit<BudgetFilters, 'categoryId'> = {}) {
  return useQuery({
    queryKey: budgetKeys.byCategory(categoryId),
    queryFn: () => budgetsApi.getByCategory(categoryId, filters),
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * Hook to fetch budgets by family member
 */
export function useBudgetsByFamilyMember(familyMemberId: string, filters: Omit<BudgetFilters, 'familyMemberId'> = {}) {
  return useQuery({
    queryKey: budgetKeys.byFamilyMember(familyMemberId),
    queryFn: () => budgetsApi.getByFamilyMember(familyMemberId, filters),
    enabled: !!familyMemberId,
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * Hook to fetch budgets by period
 */
export function useBudgetsByPeriod(month: number, year: number, filters: Omit<BudgetFilters, 'month' | 'year'> = {}) {
  return useQuery({
    queryKey: budgetKeys.byPeriod(month, year),
    queryFn: () => budgetsApi.getByPeriod(month, year, filters),
    enabled: !!month && !!year,
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * Hook to fetch current month budgets
 */
export function useCurrentMonthBudgets(filters: Omit<BudgetFilters, 'month' | 'year'> = {}) {
  return useQuery({
    queryKey: budgetKeys.currentMonth(),
    queryFn: () => budgetsApi.getCurrentMonth(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to create a new budget
 */
export function useCreateBudget() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateBudgetRequest) => budgetsApi.create(data),
    onSuccess: (response) => {
      // Safety check
      if (!response) {
        console.error('Invalid response in budget creation:', response)
        toast.error('Erro interno ao criar orçamento')
        return
      }

      // Invalidate and refetch budget lists - more aggressive invalidation for grouping
      queryClient.invalidateQueries({ queryKey: budgetKeys.all })
      queryClient.invalidateQueries({ queryKey: budgetKeys.lists() })
      queryClient.invalidateQueries({ queryKey: budgetKeys.stats() })
      queryClient.invalidateQueries({ queryKey: budgetKeys.currentMonth() })

      // If the budget has a category, invalidate category-specific queries
      if (response.categoryId) {
        queryClient.invalidateQueries({ queryKey: budgetKeys.byCategory(response.categoryId) })
      }

      // If the budget has a family member, invalidate family member-specific queries
      if (response.familyMemberId) {
        queryClient.invalidateQueries({ queryKey: budgetKeys.byFamilyMember(response.familyMemberId) })
      }

      toast.success('Orçamento criado com sucesso!')
    },
    onError: (error: any) => {
      const errorMessage = error?.error?.message || error?.message || 'Erro ao criar orçamento'
      toast.error(errorMessage)
    },
  })
}

/**
 * Hook to update an existing budget
 */
export function useUpdateBudget() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBudgetRequest }) =>
      budgetsApi.update(id, data),
    onSuccess: (response, { id }) => {
      // Safety checks
      if (!response || !id) {
        console.error('Invalid response or id in budget update:', { response, id })
        toast.error('Erro interno ao atualizar orçamento')
        return
      }

      // Update the budget in the cache
      queryClient.setQueryData(budgetKeys.detail(id), response)

      // Invalidate lists to reflect changes - more aggressive invalidation for grouping
      queryClient.invalidateQueries({ queryKey: budgetKeys.all })
      queryClient.invalidateQueries({ queryKey: budgetKeys.lists() })
      queryClient.invalidateQueries({ queryKey: budgetKeys.stats() })
      queryClient.invalidateQueries({ queryKey: budgetKeys.currentMonth() })

      // Invalidate progress for this budget
      queryClient.invalidateQueries({ queryKey: budgetKeys.progress(id) })

      toast.success('Orçamento atualizado com sucesso!')
    },
    onError: (error: any) => {
      const errorMessage = error?.error?.message || error?.message || 'Erro ao atualizar orçamento'
      toast.error(errorMessage)
    },
  })
}

/**
 * Hook to delete a budget
 */
export function useDeleteBudget() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => budgetsApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: budgetKeys.detail(id) })
      queryClient.removeQueries({ queryKey: budgetKeys.progress(id) })
      
      // Invalidate lists and stats - more aggressive invalidation for grouping
      queryClient.invalidateQueries({ queryKey: budgetKeys.all })
      queryClient.invalidateQueries({ queryKey: budgetKeys.lists() })
      queryClient.invalidateQueries({ queryKey: budgetKeys.stats() })
      queryClient.invalidateQueries({ queryKey: budgetKeys.currentMonth() })
      
      toast.success('Orçamento excluído com sucesso!')
    },
    onError: (error: any) => {
      const errorMessage = error?.error?.message || error?.message || 'Erro ao excluir orçamento'
      toast.error(errorMessage)
    },
  })
}

/**
 * Hook to get over-budget items
 */
export function useOverBudgetItems(filters: BudgetFilters = {}) {
  return useQuery({
    queryKey: [...budgetKeys.lists(), 'over-budget', filters],
    queryFn: () => budgetsApi.getOverBudget(filters),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

/**
 * Hook to get budgets needing attention
 */
export function useBudgetsNeedingAttention(filters: BudgetFilters = {}) {
  return useQuery({
    queryKey: [...budgetKeys.lists(), 'needing-attention', filters],
    queryFn: () => budgetsApi.getNeedingAttention(filters),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}
