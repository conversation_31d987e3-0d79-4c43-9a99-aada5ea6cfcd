import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  name?: string
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    console.error('🚨 ERROR BOUNDARY: getDerivedStateFromError', error)
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 ERROR BOUNDARY CAUGHT ERROR:', {
      boundaryName: this.props.name || 'Unknown',
      error,
      errorInfo,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    })

    // Check if this is our target error
    if (error.message.includes("Cannot read properties of undefined (reading 'id')")) {
      console.error('🎯 TARGET ERROR CAUGHT BY BOUNDARY!', {
        boundaryName: this.props.name,
        error,
        errorInfo,
        fullStack: error.stack,
        componentStack: errorInfo.componentStack
      })
    }

    this.setState({ error, errorInfo })
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-4 border border-red-500 bg-red-50 rounded">
          <h2 className="text-red-800 font-bold">Erro capturado por {this.props.name || 'ErrorBoundary'}</h2>
          <details className="mt-2">
            <summary className="cursor-pointer text-red-600">Detalhes do erro</summary>
            <pre className="mt-2 text-xs text-red-700 overflow-auto">
              {this.state.error?.message}
              {'\n\n'}
              {this.state.error?.stack}
            </pre>
          </details>
        </div>
      )
    }

    return this.props.children
  }
}
