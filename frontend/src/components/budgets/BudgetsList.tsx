import { useState, useMemo, useEffect } from 'react'
import { Calculator, Search, Filter, AlertTriangle, CheckCircle } from 'lucide-react'
import { BudgetCard } from './BudgetCard'
import { BudgetsFilters } from './BudgetsFilters'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useDebounce } from '@/hooks/useDebounce'
import { groupBudgetsByParentCategory } from '@/lib/budget-grouping'
import type { Budget, BudgetFilters } from '@/types/budget.types'

interface BudgetsListProps {
  budgets: Budget[]
  isLoading?: boolean
  onEdit: (budget: Budget) => void
  onDelete: (budgetId: string) => void
  onViewDetails?: (budget: Budget) => void
  filters?: BudgetFilters
  onFiltersChange?: (filters: BudgetFilters) => void
}

interface BudgetsListSkeletonProps {
  count?: number
}

function BudgetsListSkeleton({ count = 6 }: BudgetsListSkeletonProps) {
  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="glass-deep p-6 rounded-xl shadow-elegant">
          {/* Header skeleton */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-xl bg-secondary-800 animate-pulse" />
              <div className="space-y-2">
                <div className="h-5 w-32 bg-secondary-800 rounded animate-pulse" />
                <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse" />
              </div>
            </div>
            <div className="h-8 w-8 bg-secondary-800 rounded animate-pulse" />
          </div>

          {/* Progress skeleton */}
          <div className="space-y-3 mb-4">
            <div className="flex justify-between">
              <div className="h-4 w-20 bg-secondary-800 rounded animate-pulse" />
              <div className="h-6 w-16 bg-secondary-800 rounded animate-pulse" />
            </div>
            <div className="h-3 w-full bg-secondary-800 rounded animate-pulse" />
          </div>

          {/* Stats skeleton */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-border/50">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="text-center space-y-1">
                <div className="h-3 w-12 bg-secondary-800 rounded animate-pulse mx-auto" />
                <div className="h-5 w-16 bg-secondary-800 rounded animate-pulse mx-auto" />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

export function BudgetsList({
  budgets,
  isLoading,
  onEdit,
  onDelete,
  onViewDetails,
  filters = {},
  onFiltersChange
}: BudgetsListProps) {
  console.log('🔍 DEBUG: BudgetsList render', {
    budgetsCount: budgets?.length,
    budgets: budgets?.map(b => ({ id: b.id, type: b.type, categoryName: b.category?.name })),
    isLoading,
    timestamp: new Date().toISOString()
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [localFilters, setLocalFilters] = useState<BudgetFilters>(filters)

  const debouncedSearchTerm = useDebounce(searchTerm, 300)

  // Group budgets by parent category and filter by search term
  const processedBudgets = useMemo(() => {
    // First group budgets by parent category
    const groupedBudgets = groupBudgetsByParentCategory(budgets)

    // Then filter by search term
    if (!debouncedSearchTerm) return groupedBudgets

    const searchLower = debouncedSearchTerm.toLowerCase()
    return groupedBudgets.filter(budget => {
      // Search in main category name
      if (budget.category.name.toLowerCase().includes(searchLower)) return true

      // Search in family member name
      if (budget.familyMember?.name.toLowerCase().includes(searchLower)) return true

      // For grouped budgets, also search in child categories
      if (budget.type === 'grouped' && budget.childBudgets) {
        return budget.childBudgets.some(child =>
          child.category.name.toLowerCase().includes(searchLower) ||
          child.familyMember?.name.toLowerCase().includes(searchLower)
        )
      }

      return false
    })
  }, [budgets, debouncedSearchTerm])

  // Group processed budgets by status for better organization
  const groupedBudgetsByStatus = useMemo(() => ({
    over_budget: processedBudgets.filter(b => b.status === 'over_budget'),
    on_track: processedBudgets.filter(b => b.status === 'on_track'),
    under_budget: processedBudgets.filter(b => b.status === 'under_budget'),
  }), [processedBudgets])

  // Force re-render when budgets change to ensure grouping is updated
  useEffect(() => {
    // This effect ensures the component re-renders when budgets change
    // helping with the grouping logic updates
  }, [budgets])

  const handleFiltersChange = (newFilters: BudgetFilters) => {
    setLocalFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  const handleClearFilters = () => {
    setLocalFilters({})
    onFiltersChange?.({})
  }

  if (isLoading) {
    return <BudgetsListSkeleton />
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="glass-deep p-4 rounded-xl shadow-elegant">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar orçamentos por categoria ou membro..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Toggle */}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filtros
            {Object.keys(localFilters).length > 0 && (
              <span className="ml-1 px-2 py-0.5 bg-primary text-primary-foreground text-xs rounded-full">
                {Object.keys(localFilters).length}
              </span>
            )}
          </Button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-border/50">
            <BudgetsFilters
              filters={localFilters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={handleClearFilters}
            />
          </div>
        )}
      </div>

      {/* Results Summary */}
      {processedBudgets.length > 0 && (
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            {processedBudgets.length} orçamento{processedBudgets.length !== 1 ? 's' : ''} encontrado{processedBudgets.length !== 1 ? 's' : ''}
          </span>
          {debouncedSearchTerm && (
            <span>
              Buscando por: "{debouncedSearchTerm}"
            </span>
          )}
        </div>
      )}

      {/* Budgets Grid */}
      {processedBudgets.length === 0 ? (
        <div className="text-center py-12">
          <Calculator className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            {debouncedSearchTerm ? 'Nenhum orçamento encontrado' : 'Nenhum orçamento cadastrado'}
          </h3>
          <p className="text-muted-foreground max-w-md mx-auto">
            {debouncedSearchTerm 
              ? 'Tente ajustar os filtros ou termo de busca para encontrar orçamentos.'
              : 'Comece criando seu primeiro orçamento para controlar seus gastos por categoria.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Over Budget - Priority Section */}
          {groupedBudgetsByStatus.over_budget.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <h3 className="text-lg font-semibold text-red-600">
                  Orçamentos Estourados ({groupedBudgetsByStatus.over_budget.length})
                </h3>
              </div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {groupedBudgetsByStatus.over_budget.map((budget) => (
                  <BudgetCard
                    key={budget.id}
                    budget={budget}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onViewDetails={onViewDetails}
                  />
                ))}
              </div>
            </div>
          )}

          {/* On Track */}
          {groupedBudgetsByStatus.on_track.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-blue-600">
                  No Caminho Certo ({groupedBudgetsByStatus.on_track.length})
                </h3>
              </div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {groupedBudgetsByStatus.on_track.map((budget) => (
                  <BudgetCard
                    key={budget.id}
                    budget={budget}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onViewDetails={onViewDetails}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Under Budget */}
          {groupedBudgetsByStatus.under_budget.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold text-green-600">
                  Abaixo do Orçamento ({groupedBudgetsByStatus.under_budget.length})
                </h3>
              </div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {groupedBudgetsByStatus.under_budget.map((budget) => (
                  <BudgetCard
                    key={budget.id}
                    budget={budget}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onViewDetails={onViewDetails}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
